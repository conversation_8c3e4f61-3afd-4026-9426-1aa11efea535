#!/usr/bin/env python3
"""
工站監控系統主程式入口
"""

import sys
import argparse
from pathlib import Path

# 添加src目錄到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.workstation_monitor import WorkstationMonitorSystem
from loguru import logger


def parse_arguments():
    """解析命令行參數"""
    parser = argparse.ArgumentParser(description='工站停留與離開時間監控系統')
    
    parser.add_argument(
        '--config', '-c',
        type=str,
        default='config/config.yaml',
        help='配置文件路径 (默認: config/config.yaml)'
    )
    
    parser.add_argument(
        '--debug', '-d',
        action='store_true',
        help='啟用調試模式'
    )
    
    parser.add_argument(
        '--test-db',
        action='store_true',
        help='僅測試數據庫連接'
    )
    
    parser.add_argument(
        '--init-db',
        action='store_true',
        help='初始化數據庫（執行SQL腳本）'
    )
    
    parser.add_argument(
        '--version', '-v',
        action='version',
        version='工站監控系統 v1.0.0'
    )
    
    return parser.parse_args()


def test_database_connection(config_path: str):
    """測試數據庫連接"""
    try:
        from src.utils.config_manager import ConfigManager
        from src.database.connection import DatabaseManager
        
        config_manager = ConfigManager(config_path)
        db_config = config_manager.get_database_config()
        
        print("正在測試數據庫連接...")
        db_manager = DatabaseManager(db_config)
        
        if db_manager.test_connection():
            print("✓ 數據庫連接成功")
            return True
        else:
            print("✗ 數據庫連接失敗")
            return False
            
    except Exception as e:
        print(f"✗ 數據庫連接測試失敗: {e}")
        return False


def initialize_database(config_path: str):
    """初始化數據庫"""
    try:
        from src.utils.config_manager import ConfigManager
        from src.database.connection import DatabaseManager
        
        config_manager = ConfigManager(config_path)
        db_config = config_manager.get_database_config()
        
        print("正在初始化數據庫...")
        db_manager = DatabaseManager(db_config)
        
        # 執行初始化SQL腳本
        sql_file = Path(__file__).parent / "sql" / "init_database.sql"
        if sql_file.exists():
            db_manager.execute_sql_file(str(sql_file))
            print("✓ 數據庫初始化完成")
            return True
        else:
            print(f"✗ SQL初始化腳本不存在: {sql_file}")
            return False
            
    except Exception as e:
        print(f"✗ 數據庫初始化失敗: {e}")
        return False


def main():
    """主函數"""
    args = parse_arguments()
    
    # 檢查配置文件是否存在
    config_path = Path(args.config)
    if not config_path.exists():
        print(f"錯誤: 配置文件不存在: {config_path}")
        print("請確保配置文件存在，或使用 --config 參數指定正確的路径")
        sys.exit(1)
    
    # 處理特殊命令
    if args.test_db:
        success = test_database_connection(str(config_path))
        sys.exit(0 if success else 1)
    
    if args.init_db:
        success = initialize_database(str(config_path))
        sys.exit(0 if success else 1)
    
    # 啟動監控系統
    try:
        print("=" * 60)
        print("工站停留與離開時間監控系統")
        print("版本: 1.0.0")
        print("=" * 60)
        
        # 創建監控系統實例
        monitor_system = WorkstationMonitorSystem(str(config_path))
        
        # 如果是調試模式，顯示系統狀態
        if args.debug:
            print("\n系統配置:")
            print(f"  配置文件: {config_path}")
            print(f"  調試模式: {args.debug}")
            
            status = monitor_system.get_system_status()
            print(f"  系統狀態: {'運行中' if status['is_running'] else '未運行'}")
        
        print("\n正在啟動監控系統...")
        print("按 Ctrl+C 停止系統")
        print("-" * 60)
        
        # 啟動系統
        monitor_system.start()
        
    except KeyboardInterrupt:
        print("\n\n接收到中斷信號，正在關閉系統...")
    except Exception as e:
        logger.error(f"系統運行錯誤: {e}")
        print(f"\n錯誤: {e}")
        sys.exit(1)
    finally:
        print("系統已關閉")


if __name__ == "__main__":
    main()
