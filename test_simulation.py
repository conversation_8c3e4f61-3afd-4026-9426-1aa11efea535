#!/usr/bin/env python3
"""
工站監控系統模擬測試
不需要真實的PLC和數據庫連接
"""

import sys
import time
from pathlib import Path

# 添加src目錄到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.config_manager import ConfigManager
from src.monitoring.station_monitor import StationMonitor
from src.monitoring.production_tracker import ProductionTracker
from src.plc.modbus_client import PLCReadResult
from loguru import logger


def test_station_monitor():
    """測試工站監控器"""
    print("=" * 50)
    print("測試工站監控器")
    print("=" * 50)
    
    # 加載配置
    config_manager = ConfigManager()
    
    # 創建工站監控器
    station_monitor = StationMonitor(config_manager.config)
    
    # 創建生產追蹤器
    production_tracker = ProductionTracker(config_manager.config)
    
    # 設置回調函數
    def on_station_arrival(event, record):
        print(f"✓ 工站到達: {event.station_name} at {event.timestamp}")
        print(f"  記錄: {record.to_json()}")
    
    def on_station_departure(event, record):
        print(f"✓ 工站離開: {event.station_name} at {event.timestamp}")
        print(f"  停留時間: {event.duration:.2f}秒")
        print(f"  記錄: {record.to_json()}")
    
    def on_production_start(cycle_id, start_time):
        print(f"✓ 生產開始: {cycle_id} at {start_time}")
        production_tracker.start_cycle(cycle_id, start_time)
    
    def on_production_end(cycle_id, end_time, total_duration):
        print(f"✓ 生產結束: {cycle_id} at {end_time}")
        print(f"  總時間: {total_duration:.2f}秒")
        production_tracker.end_cycle(cycle_id, end_time, total_duration)
    
    def on_cycle_complete(cycle_id, cycle_data):
        print(f"✓ 週期完成: {cycle_id}")
        print(f"  工站數據: {len(cycle_data.get('stations', []))} 個工站")
        production_tracker.update_cycle_data(cycle_id, cycle_data.get('stations', []))
    
    # 註冊回調
    station_monitor.add_callback('on_station_arrival', on_station_arrival)
    station_monitor.add_callback('on_station_departure', on_station_departure)
    station_monitor.add_callback('on_production_start', on_production_start)
    station_monitor.add_callback('on_production_end', on_production_end)
    station_monitor.add_callback('on_cycle_complete', on_cycle_complete)
    
    # 模擬PLC事件
    print("\n開始模擬PLC事件...")
    
    # 模擬一個完整的生產週期
    current_time = time.time()
    
    # 1. 第一個工站到達
    print(f"\n[{time.strftime('%H:%M:%S')}] 模擬: 工件到達第一個工站")
    station_monitor.on_stay_trigger(current_time)
    time.sleep(0.1)
    
    # 2. 第一個工站離開
    current_time += 5  # 停留5秒
    print(f"\n[{time.strftime('%H:%M:%S', time.localtime(current_time))}] 模擬: 工件離開第一個工站")
    station_monitor.on_leave_trigger(current_time)
    time.sleep(0.1)
    
    # 3. 第二個工站到達
    current_time += 2  # 移動2秒
    print(f"\n[{time.strftime('%H:%M:%S', time.localtime(current_time))}] 模擬: 工件到達第二個工站")
    station_monitor.on_stay_trigger(current_time)
    time.sleep(0.1)
    
    # 4. 第二個工站離開
    current_time += 8  # 停留8秒
    print(f"\n[{time.strftime('%H:%M:%S', time.localtime(current_time))}] 模擬: 工件離開第二個工站")
    station_monitor.on_leave_trigger(current_time)
    time.sleep(0.1)
    
    # 5. 模擬到達最後一站（第12站）
    for i in range(3, 12):  # 工站3到11
        current_time += 1
        station_monitor.on_stay_trigger(current_time)
        current_time += 3
        station_monitor.on_leave_trigger(current_time)
    
    # 6. 最後一站
    current_time += 1
    print(f"\n[{time.strftime('%H:%M:%S', time.localtime(current_time))}] 模擬: 工件到達最後一站")
    station_monitor.on_stay_trigger(current_time)
    time.sleep(0.1)
    
    current_time += 10  # 最後一站停留10秒
    print(f"\n[{time.strftime('%H:%M:%S', time.localtime(current_time))}] 模擬: 工件離開最後一站（生產結束）")
    station_monitor.on_leave_trigger(current_time)
    time.sleep(0.1)
    
    # 顯示最終狀態
    print("\n" + "=" * 50)
    print("最終狀態")
    print("=" * 50)
    
    print("\n工站狀態:")
    station_status = station_monitor.get_station_status()
    for station_id, status in station_status.items():
        if status['arrival_time'] or status['leave_time']:
            print(f"  {status['station_name']}: {status['state']}")
            if status['arrival_time']:
                print(f"    到達時間: {status['arrival_time']}")
            if status['stay_duration']:
                print(f"    停留時間: {status['stay_duration']:.2f}秒")
            if status['leave_time']:
                print(f"    離開時間: {status['leave_time']}")
    
    print("\n生產統計:")
    stats = production_tracker.get_statistics()
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    print("\n效率指標:")
    efficiency = production_tracker.get_efficiency_metrics()
    for key, value in efficiency.items():
        print(f"  {key}: {value}")


def test_plc_client():
    """測試PLC客戶端（不實際連接）"""
    print("\n" + "=" * 50)
    print("測試PLC客戶端（模擬）")
    print("=" * 50)
    
    from src.plc.modbus_client import ModbusTCPClient
    
    # 創建客戶端（不會實際連接）
    client = ModbusTCPClient("***********", 502)
    
    print(f"PLC客戶端配置:")
    print(f"  主機: {client.host}")
    print(f"  端口: {client.port}")
    print(f"  超時: {client.timeout}秒")
    print(f"  重試次數: {client.retry_count}")
    
    # 獲取連接狀態
    status = client.get_connection_status()
    print(f"\n連接狀態:")
    for key, value in status.items():
        print(f"  {key}: {value}")
    
    # 模擬讀取結果
    print(f"\n模擬PLC讀取結果:")
    mock_result = PLCReadResult(
        success=True,
        timestamp=time.time(),
        data={
            'stay_trigger': True,
            'leave_trigger': False
        }
    )
    
    print(f"  成功: {mock_result.success}")
    print(f"  時間戳: {mock_result.timestamp}")
    print(f"  數據: {mock_result.data}")


def main():
    """主函數"""
    print("工站監控系統模擬測試")
    print("版本: 1.0.0")
    print("這個測試不需要真實的PLC和數據庫連接")
    
    try:
        # 測試PLC客戶端
        test_plc_client()
        
        # 測試工站監控器
        test_station_monitor()
        
        print("\n" + "=" * 50)
        print("✓ 所有測試完成！")
        print("=" * 50)
        print("\n系統功能驗證:")
        print("  ✓ 配置文件加載")
        print("  ✓ 日誌系統")
        print("  ✓ PLC客戶端模擬")
        print("  ✓ 工站狀態監控")
        print("  ✓ 生產週期追蹤")
        print("  ✓ 事件回調機制")
        print("  ✓ 統計信息計算")
        
        print("\n下一步:")
        print("  1. 安裝MariaDB數據庫")
        print("  2. 配置PLC網路連接")
        print("  3. 運行完整系統: python main.py")
        
    except Exception as e:
        print(f"\n錯誤: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
