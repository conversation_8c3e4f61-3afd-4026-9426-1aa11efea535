#!/bin/bash
# 工站監控系統安裝腳本 - 適用於樹莓派/Ubuntu

set -e

echo "=========================================="
echo "工站停留與離開時間監控系統安裝腳本"
echo "版本: 1.0.0"
echo "=========================================="

# 檢查是否為root用戶
if [[ $EUID -eq 0 ]]; then
   echo "請不要使用root用戶運行此腳本"
   exit 1
fi

# 獲取腳本所在目錄
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo "項目目錄: $PROJECT_DIR"

# 更新系統包
echo "更新系統包..."
sudo apt update
sudo apt upgrade -y

# 安裝系統依賴
echo "安裝系統依賴..."
sudo apt install -y \
    python3 \
    python3-pip \
    python3-venv \
    mariadb-server \
    mariadb-client \
    git \
    curl \
    wget \
    nano \
    htop \
    systemd

# 創建虛擬環境
echo "創建Python虛擬環境..."
cd "$PROJECT_DIR"
python3 -m venv venv
source venv/bin/activate

# 升級pip
echo "升級pip..."
pip install --upgrade pip

# 安裝Python依賴
echo "安裝Python依賴..."
pip install -r requirements.txt

# 配置MariaDB
echo "配置MariaDB..."
sudo systemctl start mariadb
sudo systemctl enable mariadb

# 檢查MariaDB是否運行
if ! sudo systemctl is-active --quiet mariadb; then
    echo "錯誤: MariaDB服務未運行"
    exit 1
fi

# 創建數據庫和用戶
echo "初始化數據庫..."
sudo mysql -e "CREATE DATABASE IF NOT EXISTS workstation_monitoring CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
sudo mysql -e "CREATE USER IF NOT EXISTS 'workstation_user'@'localhost' IDENTIFIED BY 'workstation_pass';"
sudo mysql -e "GRANT ALL PRIVILEGES ON workstation_monitoring.* TO 'workstation_user'@'localhost';"
sudo mysql -e "FLUSH PRIVILEGES;"

# 執行數據庫初始化腳本
echo "執行數據庫初始化腳本..."
mysql -u workstation_user -pworkstation_pass workstation_monitoring < sql/init_database.sql

# 創建日誌目錄
echo "創建日誌目錄..."
mkdir -p logs
chmod 755 logs

# 創建系統用戶（如果不存在）
if ! id "workstation" &>/dev/null; then
    echo "創建系統用戶..."
    sudo useradd -r -s /bin/false -d "$PROJECT_DIR" workstation
fi

# 設置文件權限
echo "設置文件權限..."
sudo chown -R workstation:workstation "$PROJECT_DIR"
sudo chmod +x main.py
sudo chmod +x deploy/*.sh

# 複製systemd服務文件
echo "安裝systemd服務..."
sudo cp deploy/workstation-monitor.service /etc/systemd/system/
sudo sed -i "s|/path/to/project|$PROJECT_DIR|g" /etc/systemd/system/workstation-monitor.service
sudo systemctl daemon-reload

# 測試配置
echo "測試系統配置..."
source venv/bin/activate
python main.py --test-db

if [ $? -eq 0 ]; then
    echo "✓ 數據庫連接測試成功"
else
    echo "✗ 數據庫連接測試失敗"
    exit 1
fi

# 啟用服務
echo "啟用監控服務..."
sudo systemctl enable workstation-monitor.service

echo ""
echo "=========================================="
echo "安裝完成！"
echo "=========================================="
echo ""
echo "使用以下命令管理服務:"
echo "  啟動服務: sudo systemctl start workstation-monitor"
echo "  停止服務: sudo systemctl stop workstation-monitor"
echo "  查看狀態: sudo systemctl status workstation-monitor"
echo "  查看日誌: sudo journalctl -u workstation-monitor -f"
echo ""
echo "配置文件位置: $PROJECT_DIR/config/config.yaml"
echo "日誌文件位置: $PROJECT_DIR/logs/"
echo ""
echo "請根據實際環境修改配置文件中的PLC IP地址等參數"
echo ""
