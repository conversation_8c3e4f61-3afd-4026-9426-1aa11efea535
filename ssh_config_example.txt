# SSH配置文件範例
# 將此內容複製到 C:\Users\<USER>\.ssh\config

# 樹莓派連接配置
Host raspberry-pi
    HostName *************  # 替換為您的樹莓派IP地址
    User pi                 # 樹莓派用戶名
    Port 22                 # SSH端口，默認22
    # IdentityFile ~/.ssh/id_rsa  # 如果使用SSH密鑰

# 如果您有多個樹莓派
Host pi-workstation
    HostName ***********   # 工站監控專用樹莓派IP
    User workstation       # 專用用戶名
    Port 22
    # 可以設置其他選項
    ServerAliveInterval 60
    ServerAliveCountMax 3

# 通用設置
Host *
    ServerAliveInterval 60
    ServerAliveCountMax 3
    TCPKeepAlive yes
