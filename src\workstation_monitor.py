"""
工站監控系統主類
整合所有模組，提供統一的系統接口
"""

import time
import signal
import threading
from typing import Dict, Any, Optional
from datetime import datetime
from loguru import logger

from .utils.config_manager import ConfigManager
from .utils.logger_setup import setup_logger
from .database.connection import initialize_database, DatabaseManager
from .database.data_manager import DataManager
from .plc.plc_manager import PLCManager
from .monitoring.station_monitor import StationMonitor, StationRecord, StationEvent
from .monitoring.production_tracker import ProductionTracker, ProductionCycleData


class WorkstationMonitorSystem:
    """工站監控系統主類"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化工站監控系統
        
        Args:
            config_path: 配置文件路径
        """
        self.config_manager = ConfigManager(config_path)
        self.is_running = False
        self.shutdown_event = threading.Event()
        
        # 初始化日誌
        setup_logger(self.config_manager.get_logging_config())
        logger.info("工站監控系統初始化開始")
        
        # 驗證配置
        if not self.config_manager.validate_config():
            raise RuntimeError("配置驗證失敗")
        
        # 初始化組件
        self.db_manager: Optional[DatabaseManager] = None
        self.data_manager: Optional[DataManager] = None
        self.plc_manager: Optional[PLCManager] = None
        self.station_monitor: Optional[StationMonitor] = None
        self.production_tracker: Optional[ProductionTracker] = None
        
        # 統計信息
        self.start_time: Optional[datetime] = None
        self.total_events_processed = 0
        self.last_heartbeat = datetime.now()
        
        # 初始化所有組件
        self._initialize_components()
        
        # 設置信號處理
        self._setup_signal_handlers()
        
        logger.info("工站監控系統初始化完成")
    
    def _initialize_components(self):
        """初始化所有系統組件"""
        try:
            # 初始化數據庫
            db_config = self.config_manager.get_database_config()
            self.db_manager = initialize_database(db_config)
            
            # 測試數據庫連接
            if not self.db_manager.test_connection():
                raise RuntimeError("數據庫連接失敗")
            
            # 創建數據表
            self.db_manager.create_tables()
            
            # 初始化數據管理器
            self.data_manager = DataManager(self.db_manager)
            
            # 初始化PLC管理器
            plc_config = self.config_manager.get_plc_config()
            self.plc_manager = PLCManager(plc_config)
            
            # 初始化工站監控器
            self.station_monitor = StationMonitor(self.config_manager.config)
            
            # 初始化生產追蹤器
            self.production_tracker = ProductionTracker(self.config_manager.config)
            
            # 設置回調函數
            self._setup_callbacks()
            
            logger.info("所有系統組件初始化完成")
            
        except Exception as e:
            logger.error(f"組件初始化失敗: {e}")
            raise
    
    def _setup_callbacks(self):
        """設置各組件間的回調函數"""
        # PLC事件回調
        self.plc_manager.add_callback('on_stay_trigger', self._on_stay_trigger)
        self.plc_manager.add_callback('on_leave_trigger', self._on_leave_trigger)
        self.plc_manager.add_callback('on_connection_change', self._on_plc_connection_change)
        self.plc_manager.add_callback('on_error', self._on_plc_error)
        
        # 工站監控事件回調
        self.station_monitor.add_callback('on_station_arrival', self._on_station_arrival)
        self.station_monitor.add_callback('on_station_departure', self._on_station_departure)
        self.station_monitor.add_callback('on_production_start', self._on_production_start)
        self.station_monitor.add_callback('on_production_end', self._on_production_end)
        self.station_monitor.add_callback('on_cycle_complete', self._on_cycle_complete)
        
        # 生產追蹤事件回調
        self.production_tracker.add_callback('on_cycle_timeout', self._on_cycle_timeout)
        self.production_tracker.add_callback('on_statistics_update', self._on_statistics_update)
        
        logger.info("回調函數設置完成")
    
    def _setup_signal_handlers(self):
        """設置信號處理器"""
        def signal_handler(signum, frame):
            logger.info(f"接收到信號 {signum}，開始關閉系統...")
            self.shutdown()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def start(self):
        """啟動監控系統"""
        if self.is_running:
            logger.warning("系統已在運行中")
            return
        
        logger.info("啟動工站監控系統...")
        self.start_time = datetime.now()
        self.is_running = True
        
        try:
            # 更新系統狀態
            self.data_manager.update_system_status('MONITOR', 'ONLINE')
            
            # 啟動PLC監控
            self.plc_manager.start_monitoring()
            
            # 啟動心跳線程
            heartbeat_thread = threading.Thread(target=self._heartbeat_loop, daemon=True)
            heartbeat_thread.start()
            
            logger.info("工站監控系統啟動成功")
            
            # 主循環
            self._main_loop()
            
        except Exception as e:
            logger.error(f"系統啟動失敗: {e}")
            self.shutdown()
            raise
    
    def shutdown(self):
        """關閉監控系統"""
        if not self.is_running:
            return
        
        logger.info("關閉工站監控系統...")
        self.is_running = False
        self.shutdown_event.set()
        
        try:
            # 停止PLC監控
            if self.plc_manager:
                self.plc_manager.stop_monitoring()
                self.plc_manager.disconnect()
            
            # 更新系統狀態
            if self.data_manager:
                self.data_manager.update_system_status('MONITOR', 'OFFLINE')
                self.data_manager.update_system_status('PLC', 'OFFLINE')
            
            # 關閉數據庫連接
            if self.db_manager:
                self.db_manager.close()
            
            logger.info("工站監控系統已關閉")
            
        except Exception as e:
            logger.error(f"系統關閉時發生錯誤: {e}")
    
    def _main_loop(self):
        """主循環"""
        logger.info("進入主循環")
        
        while self.is_running and not self.shutdown_event.is_set():
            try:
                # 檢查生產超時
                if self.production_tracker:
                    self.production_tracker.check_timeout()
                
                # 更新心跳
                self.last_heartbeat = datetime.now()
                
                # 等待一段時間
                self.shutdown_event.wait(1.0)
                
            except Exception as e:
                logger.error(f"主循環錯誤: {e}")
                time.sleep(1)
        
        logger.info("主循環結束")
    
    def _heartbeat_loop(self):
        """心跳循環"""
        while self.is_running:
            try:
                # 更新系統狀態
                metadata = {
                    'uptime_seconds': (datetime.now() - self.start_time).total_seconds() if self.start_time else 0,
                    'events_processed': self.total_events_processed,
                    'last_heartbeat': self.last_heartbeat.isoformat()
                }
                
                self.data_manager.update_system_status('MONITOR', 'ONLINE', extra_data=metadata)
                
                time.sleep(30)  # 每30秒更新一次心跳
                
            except Exception as e:
                logger.error(f"心跳更新錯誤: {e}")
                time.sleep(5)
    
    # PLC事件處理器
    def _on_stay_trigger(self, timestamp: float):
        """處理停留觸發事件"""
        logger.info(f"處理停留觸發: {timestamp}")
        self.station_monitor.on_stay_trigger(timestamp)
        self.total_events_processed += 1
    
    def _on_leave_trigger(self, timestamp: float):
        """處理離開觸發事件"""
        logger.info(f"處理離開觸發: {timestamp}")
        self.station_monitor.on_leave_trigger(timestamp)
        self.total_events_processed += 1
    
    def _on_plc_connection_change(self, connected: bool, message: str):
        """處理PLC連接狀態變化"""
        status = 'ONLINE' if connected else 'OFFLINE'
        error_msg = None if connected else message
        self.data_manager.update_system_status('PLC', status, error_message=error_msg)
        logger.info(f"PLC連接狀態變化: {status} - {message}")
    
    def _on_plc_error(self, error_type: str, error_message: str):
        """處理PLC錯誤"""
        self.data_manager.update_system_status('PLC', 'ERROR', error_message=f"{error_type}: {error_message}")
        logger.error(f"PLC錯誤: {error_type} - {error_message}")
    
    # 工站監控事件處理器
    def _on_station_arrival(self, event: StationEvent, record: StationRecord):
        """處理工站到達事件"""
        logger.info(f"工站到達: {event.station_name}")
        self.data_manager.save_station_record(record)
    
    def _on_station_departure(self, event: StationEvent, record: StationRecord):
        """處理工站離開事件"""
        logger.info(f"工站離開: {event.station_name}, 停留時間: {event.duration}秒")
        self.data_manager.save_station_record(record)
    
    def _on_production_start(self, cycle_id: str, start_time: datetime):
        """處理生產開始事件"""
        logger.info(f"生產開始: {cycle_id}")
        self.production_tracker.start_cycle(cycle_id, start_time)
    
    def _on_production_end(self, cycle_id: str, end_time: datetime, total_duration: float):
        """處理生產結束事件"""
        logger.info(f"生產結束: {cycle_id}, 總時間: {total_duration}秒")
        self.production_tracker.end_cycle(cycle_id, end_time, total_duration)
    
    def _on_cycle_complete(self, cycle_id: str, cycle_data: Dict[str, Any]):
        """處理週期完成事件"""
        logger.info(f"週期完成: {cycle_id}")
        self.production_tracker.update_cycle_data(cycle_id, cycle_data.get('stations', []))
        
        # 保存週期數據
        if self.production_tracker.current_cycle:
            self.data_manager.save_production_cycle(self.production_tracker.current_cycle)
    
    # 生產追蹤事件處理器
    def _on_cycle_timeout(self, cycle_id: str, elapsed_time: float):
        """處理週期超時事件"""
        logger.warning(f"生產週期超時: {cycle_id}, 已運行 {elapsed_time}秒")
    
    def _on_statistics_update(self, statistics: Dict[str, Any]):
        """處理統計更新事件"""
        logger.debug(f"統計信息更新: 總週期數 {statistics.get('total_cycles', 0)}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """獲取系統狀態"""
        return {
            'is_running': self.is_running,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'uptime_seconds': (datetime.now() - self.start_time).total_seconds() if self.start_time else 0,
            'events_processed': self.total_events_processed,
            'last_heartbeat': self.last_heartbeat.isoformat(),
            'plc_status': self.plc_manager.get_status() if self.plc_manager else {},
            'station_status': self.station_monitor.get_station_status() if self.station_monitor else {},
            'production_statistics': self.production_tracker.get_statistics() if self.production_tracker else {}
        }
