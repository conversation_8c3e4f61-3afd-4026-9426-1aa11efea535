# 手工生產線工站停留與離開時間監控系統

## 專案描述
本專案旨在開發一套針對手工生產線的狀態監控系統，記錄每個工站的「停留時間」（ON）與「離開時間」（OFF）。生產線包含12個人工工站，每個工站均透過Sensor連接至匯川PLC，並通過Modbus TCP協議進行設備狀態數據採集。

## 系統架構
- **生產線硬體**: 12個工站，每站對應一對感測輸入（X11到X0）
- **PLC配置**: 匯川PLC (IP: ***********, Port: 502)
- **數據採集**: 樹莓派作為數據採集服務器
- **數據存儲**: MariaDB數據庫，JSON格式存儲

## 專案結構
```
CT/
├── src/                    # 源代碼目錄
│   ├── plc/               # PLC通訊模組
│   ├── database/          # 數據庫操作模組
│   ├── monitoring/        # 監控邏輯模組
│   └── utils/             # 工具函數
├── config/                # 配置文件
├── sql/                   # 數據庫腳本
├── tests/                 # 測試文件
├── logs/                  # 日誌文件
├── requirements.txt       # Python依賴
└── main.py               # 主程式入口
```

## 功能需求
1. **PLC數據採集**: 每秒讀取PLC Modbus TCP的寄存器狀態
2. **數據存儲**: 記錄到MariaDB數據庫，JSON格式
3. **生產結束判定**: 第12站離開事件作為生產結束時間

## 數據格式範例
```json
{
    "station": "Station 12",
    "arrival_time": "2023-11-01T10:15:00",
    "stay_duration": 120,
    "leave_time": "2023-11-01T10:17:00",
    "off_duration": 300,
    "end_time": "2023-11-01T10:25:00"
}
```

## 安裝與運行
1. 安裝依賴: `pip install -r requirements.txt`
2. 配置數據庫: 執行 `sql/init_database.sql`
3. 修改配置: 編輯 `config/config.yaml`
4. 運行系統: `python main.py`

## 硬體需求
- 樹莓派 4B 或更高版本
- 網路連接至PLC (***********:502)
- MariaDB數據庫服務

## 開發環境
- Python 3.8+
- MariaDB 10.5+
- pymodbus, pymysql, pyyaml
