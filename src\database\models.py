"""
數據庫模型定義
使用SQLAlchemy ORM定義數據表結構
"""

from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import (
    Column, Integer, String, DateTime, Text, JSON, 
    Enum, TIMESTAMP, Index, create_engine
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.sql import func
import enum

Base = declarative_base()


class ProductionStatus(enum.Enum):
    """生產狀態枚舉"""
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    ABORTED = "ABORTED"


class SystemComponentStatus(enum.Enum):
    """系統組件狀態枚舉"""
    ONLINE = "ONLINE"
    OFFLINE = "OFFLINE"
    ERROR = "ERROR"


class WorkstationLog(Base):
    """工站停留與離開時間記錄表"""
    __tablename__ = 'workstation_log'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主鍵，自動遞增')
    station = Column(String(20), nullable=False, comment='工站名稱（如 Station 12）')
    arrival_time = Column(DateTime, nullable=True, comment='停留（ON）發生時間')
    stay_duration = Column(Integer, nullable=True, comment='停留（ON）的持續時間（秒）')
    leave_time = Column(DateTime, nullable=True, comment='離開（OFF）發生時間')
    off_duration = Column(Integer, nullable=True, comment='離開（OFF）的持續時間（秒）')
    end_time = Column(DateTime, nullable=True, comment='本次生產結束時間（第12站時記錄）')
    data_json = Column(JSON, nullable=True, comment='完整數據的JSON格式')
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment='記錄創建時間')
    updated_at = Column(
        TIMESTAMP, 
        default=func.current_timestamp(), 
        onupdate=func.current_timestamp(),
        comment='記錄更新時間'
    )
    
    # 索引
    __table_args__ = (
        Index('idx_station', 'station'),
        Index('idx_arrival_time', 'arrival_time'),
        Index('idx_leave_time', 'leave_time'),
        Index('idx_end_time', 'end_time'),
        Index('idx_created_at', 'created_at'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            'id': self.id,
            'station': self.station,
            'arrival_time': self.arrival_time.isoformat() if self.arrival_time else None,
            'stay_duration': self.stay_duration,
            'leave_time': self.leave_time.isoformat() if self.leave_time else None,
            'off_duration': self.off_duration,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'data_json': self.data_json,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class ProductionCycle(Base):
    """生產週期記錄表"""
    __tablename__ = 'production_cycle'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主鍵，自動遞增')
    cycle_id = Column(String(50), unique=True, nullable=False, comment='生產週期唯一標識')
    start_time = Column(DateTime, nullable=False, comment='生產開始時間')
    end_time = Column(DateTime, nullable=True, comment='生產結束時間')
    total_duration = Column(Integer, nullable=True, comment='總生產時間（秒）')
    status = Column(
        Enum(ProductionStatus), 
        default=ProductionStatus.RUNNING, 
        comment='生產狀態'
    )
    stations_data = Column(JSON, nullable=True, comment='所有工站數據的JSON格式')
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment='記錄創建時間')
    updated_at = Column(
        TIMESTAMP, 
        default=func.current_timestamp(), 
        onupdate=func.current_timestamp(),
        comment='記錄更新時間'
    )
    
    # 索引
    __table_args__ = (
        Index('idx_cycle_id', 'cycle_id'),
        Index('idx_start_time', 'start_time'),
        Index('idx_end_time', 'end_time'),
        Index('idx_status', 'status'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            'id': self.id,
            'cycle_id': self.cycle_id,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'total_duration': self.total_duration,
            'status': self.status.value if self.status else None,
            'stations_data': self.stations_data,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class SystemStatus(Base):
    """系統狀態記錄表"""
    __tablename__ = 'system_status'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主鍵，自動遞增')
    component = Column(String(50), nullable=False, comment='組件名稱（PLC, DATABASE, MONITOR等）')
    status = Column(Enum(SystemComponentStatus), nullable=False, comment='狀態')
    last_update = Column(DateTime, nullable=False, comment='最後更新時間')
    error_message = Column(Text, nullable=True, comment='錯誤信息')
    metadata = Column(JSON, nullable=True, comment='額外的狀態信息')
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment='記錄創建時間')
    updated_at = Column(
        TIMESTAMP, 
        default=func.current_timestamp(), 
        onupdate=func.current_timestamp(),
        comment='記錄更新時間'
    )
    
    # 索引和約束
    __table_args__ = (
        Index('uk_component', 'component', unique=True),
        Index('idx_status', 'status'),
        Index('idx_last_update', 'last_update'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            'id': self.id,
            'component': self.component,
            'status': self.status.value if self.status else None,
            'last_update': self.last_update.isoformat() if self.last_update else None,
            'error_message': self.error_message,
            'metadata': self.metadata,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
