"""
日誌設置工具
配置系統日誌記錄
"""

import sys
from pathlib import Path
from loguru import logger
from typing import Dict, Any


def setup_logger(config: Dict[str, Any]):
    """
    設置日誌配置
    
    Args:
        config: 日誌配置字典
    """
    # 移除默認處理器
    logger.remove()
    
    # 獲取配置
    log_level = config.get('log_level', 'INFO')
    log_dir = Path(config.get('log_dir', 'logs'))
    log_file = config.get('log_file', 'workstation_monitor.log')
    max_file_size = config.get('max_file_size', '10MB')
    backup_count = config.get('backup_count', 5)
    log_format = config.get('log_format', 
        "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}")
    
    # 確保日誌目錄存在
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 控制台輸出
    logger.add(
        sys.stdout,
        level=log_level,
        format=log_format,
        colorize=True,
        backtrace=True,
        diagnose=True
    )
    
    # 文件輸出
    log_file_path = log_dir / log_file
    logger.add(
        log_file_path,
        level=log_level,
        format=log_format,
        rotation=max_file_size,
        retention=backup_count,
        compression="zip",
        backtrace=True,
        diagnose=True
    )
    
    # 錯誤日誌單獨文件
    error_log_path = log_dir / "error.log"
    logger.add(
        error_log_path,
        level="ERROR",
        format=log_format,
        rotation="1 day",
        retention="30 days",
        compression="zip",
        backtrace=True,
        diagnose=True
    )
    
    logger.info(f"日誌系統初始化完成，日誌級別: {log_level}")
    logger.info(f"日誌文件: {log_file_path}")
    logger.info(f"錯誤日誌: {error_log_path}")


def get_logger(name: str):
    """
    獲取指定名稱的日誌記錄器
    
    Args:
        name: 日誌記錄器名稱
        
    Returns:
        日誌記錄器實例
    """
    return logger.bind(name=name)
