@echo off
REM 部署工站監控系統到樹莓派腳本

echo ==========================================
echo 部署工站監控系統到樹莓派
echo 目標: pi@************
echo ==========================================

REM 設置變量
set PI_HOST=************
set PI_USER=pi
set PROJECT_DIR=workstation-monitor

echo 1. 創建遠程目錄...
ssh %PI_USER%@%PI_HOST% "mkdir -p ~/%PROJECT_DIR%"

echo 2. 上傳項目文件...
scp -r src config sql deploy main.py requirements.txt README.md %PI_USER%@%PI_HOST%:~/%PROJECT_DIR%/

echo 3. 設置執行權限...
ssh %PI_USER%@%PI_HOST% "chmod +x ~/%PROJECT_DIR%/deploy/*.sh ~/%PROJECT_DIR%/main.py"

echo 4. 運行安裝腳本...
ssh %PI_USER%@%PI_HOST% "cd ~/%PROJECT_DIR% && ./deploy/install.sh"

echo ==========================================
echo 部署完成！
echo ==========================================
echo.
echo 連接到樹莓派: ssh %PI_USER%@%PI_HOST%
echo 項目目錄: ~/%PROJECT_DIR%
echo 啟動服務: sudo systemctl start workstation-monitor
echo.
pause
